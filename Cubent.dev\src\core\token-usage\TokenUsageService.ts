import { EventEmitter } from "events"
import * as vscode from "vscode"

import { CloudService } from "@cubent/cloud"
import { ApiStreamUsageChunk } from "../../api/transform/stream"

// Model pricing in Cubent Units based on the documentation
const MODEL_PRICING: Record<string, number> = {
  // Anthropic Claude Models
  'claude-3.7-sonnet': 1.1,
  'claude-3.7-sonnet-thinking': 1.35,
  'claude-3.5-sonnet': 0.95,
  'claude-3.5-haiku': 0.55,
  'claude-3-haiku': 0.45,

  // OpenAI Models
  'gpt-4o': 1.1,
  'gpt-4.5-preview': 1.2,
  'gpt-4o-mini': 0.65,
  'o3-mini': 1.0,
  'o3-mini-high-reasoning': 1.1,
  'o3-mini-low-reasoning': 0.75,

  // DeepSeek Models
  'deepseek-chat': 0.35,
  'deepseek-reasoner': 0.7,

  // Google Gemini Models
  'gemini-2.5-flash': 0.3,
  'gemini-2.5-flash-thinking': 0.4,
  'gemini-2.5-pro': 0.85,
  'gemini-2.0-flash': 0.45,
  'gemini-2.0-pro': 0.70,
  'gemini-1.5-flash': 0.40,
  'gemini-1.5-pro': 0.65,

  // xAI Grok Models
  'grok-3': 1.1,
  'grok-3-mini': 0.30,
  'grok-2-vision': 0.70,

  // Default fallback for unknown models
  'default': 1.0,
};

export interface TokenUsageData {
  modelId: string;
  cubentUnitsUsed: number;
  tokensUsed?: number;
  requestsMade: number;
  sessionId?: string;
  metadata?: Record<string, any>;
}

export interface UserUsageStats {
  cubentUnitsUsed: number;
  cubentUnitsLimit: number;
  cubentUnitsRemaining: number;
  usagePercentage: number;
  canMakeRequest: boolean;
  unitsResetDate?: Date;
}

interface TokenUsageEvents {
  'usage-updated': (data: { usage: UserUsageStats }) => void;
  'limit-exceeded': (data: { usage: UserUsageStats }) => void;
  'usage-warning': (data: { usage: UserUsageStats; level: 'warning' | 'critical' }) => void;
}

export class TokenUsageService extends EventEmitter<TokenUsageEvents> {
  private context: vscode.ExtensionContext;
  private cloudService: CloudService;
  private currentUsage: UserUsageStats | null = null;

  constructor(context: vscode.ExtensionContext, cloudService: CloudService) {
    super();
    this.context = context;
    this.cloudService = cloudService;
  }

  /**
   * Calculate Cubent Units for a given model
   */
  public calculateCubentUnits(modelId: string): number {
    const normalizedModelId = this.normalizeModelId(modelId);
    return MODEL_PRICING[normalizedModelId] || MODEL_PRICING.default;
  }

  /**
   * Normalize model ID to match pricing table keys
   */
  private normalizeModelId(modelId: string): string {
    return modelId.toLowerCase()
      .replace(/[_\s]/g, '-')
      .replace(/claude-3\.7-sonnet-\(thinking\)/, 'claude-3.7-sonnet-thinking')
      .replace(/claude-3\.5-sonnet/, 'claude-3.5-sonnet')
      .replace(/claude-3\.5-haiku/, 'claude-3.5-haiku')
      .replace(/claude-3-haiku/, 'claude-3-haiku')
      .replace(/gpt-4o-mini/, 'gpt-4o-mini')
      .replace(/gpt-4\.5-preview/, 'gpt-4.5-preview')
      .replace(/o3-mini-\(high-reasoning\)/, 'o3-mini-high-reasoning')
      .replace(/o3-mini-\(low-reasoning\)/, 'o3-mini-low-reasoning')
      .replace(/gemini-2\.5-flash-\(thinking\)/, 'gemini-2.5-flash-thinking')
      .replace(/gemini-2\.5-flash/, 'gemini-2.5-flash')
      .replace(/gemini-2\.5-pro/, 'gemini-2.5-pro')
      .replace(/gemini-2\.0-flash/, 'gemini-2.0-flash')
      .replace(/gemini-2\.0-pro/, 'gemini-2.0-pro')
      .replace(/gemini-1\.5-flash/, 'gemini-1.5-flash')
      .replace(/gemini-1\.5-pro/, 'gemini-1.5-pro')
      .replace(/grok-3-mini/, 'grok-3-mini')
      .replace(/grok-2-vision/, 'grok-2-vision');
  }

  /**
   * Track token usage for a message sent to AI model
   */
  public async trackMessageUsage(
    modelId: string,
    usage: ApiStreamUsageChunk,
    sessionId?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    try {
      // Calculate Cubent Units used
      const cubentUnitsUsed = this.calculateCubentUnits(modelId);
      
      const usageData: TokenUsageData = {
        modelId,
        cubentUnitsUsed,
        tokensUsed: usage.type === 'usage' ? (usage.inputTokens || 0) + (usage.outputTokens || 0) : 0,
        requestsMade: 1,
        sessionId,
        metadata,
      };

      // Update local usage tracking
      await this.updateLocalUsage(usageData);

      // Send usage to webapp if user is authenticated
      if (this.cloudService.isAuthenticated()) {
        await this.reportUsageToWebapp(usageData);
      }

      // Check for warnings and limits
      await this.checkUsageWarnings();

    } catch (error) {
      console.error('[TokenUsageService] Error tracking usage:', error);
    }
  }

  /**
   * Update local usage tracking
   */
  private async updateLocalUsage(usageData: TokenUsageData): Promise<void> {
    const currentUsage = await this.getCurrentUsage();
    
    // Update usage
    currentUsage.cubentUnitsUsed += usageData.cubentUnitsUsed;
    currentUsage.cubentUnitsRemaining = Math.max(0, currentUsage.cubentUnitsLimit - currentUsage.cubentUnitsUsed);
    currentUsage.usagePercentage = Math.min(100, (currentUsage.cubentUnitsUsed / currentUsage.cubentUnitsLimit) * 100);
    currentUsage.canMakeRequest = currentUsage.cubentUnitsRemaining > 0;

    // Save to extension storage
    await this.context.globalState.update('cubentUnitsUsage', {
      cubentUnitsUsed: currentUsage.cubentUnitsUsed,
      lastUpdated: new Date().toISOString(),
      unitsResetDate: currentUsage.unitsResetDate?.toISOString(),
    });

    this.currentUsage = currentUsage;
    this.emit('usage-updated', { usage: currentUsage });
  }

  /**
   * Report usage to webapp
   */
  private async reportUsageToWebapp(usageData: TokenUsageData): Promise<void> {
    try {
      const credentials = this.cloudService.getCredentials();
      if (!credentials?.accessToken) {
        return;
      }

      const response = await fetch(`${this.cloudService.getApiUrl()}/api/extension/usage`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${credentials.accessToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(usageData),
      });

      if (!response.ok) {
        console.error('[TokenUsageService] Failed to report usage to webapp:', response.statusText);
      }
    } catch (error) {
      console.error('[TokenUsageService] Error reporting usage to webapp:', error);
    }
  }

  /**
   * Get current usage stats
   */
  public async getCurrentUsage(): Promise<UserUsageStats> {
    if (this.currentUsage) {
      return this.currentUsage;
    }

    // Try to get from cloud service first
    if (this.cloudService.isAuthenticated()) {
      try {
        const cloudUsage = await this.getUsageFromWebapp();
        if (cloudUsage) {
          this.currentUsage = cloudUsage;
          return cloudUsage;
        }
      } catch (error) {
        console.error('[TokenUsageService] Error getting usage from webapp:', error);
      }
    }

    // Fallback to local storage
    const localUsage = this.context.globalState.get<{
      cubentUnitsUsed: number;
      lastUpdated: string;
      unitsResetDate?: string;
    }>('cubentUnitsUsage');

    const usage: UserUsageStats = {
      cubentUnitsUsed: localUsage?.cubentUnitsUsed || 0,
      cubentUnitsLimit: 50,
      cubentUnitsRemaining: 0,
      usagePercentage: 0,
      canMakeRequest: true,
      unitsResetDate: localUsage?.unitsResetDate ? new Date(localUsage.unitsResetDate) : undefined,
    };

    usage.cubentUnitsRemaining = Math.max(0, usage.cubentUnitsLimit - usage.cubentUnitsUsed);
    usage.usagePercentage = Math.min(100, (usage.cubentUnitsUsed / usage.cubentUnitsLimit) * 100);
    usage.canMakeRequest = usage.cubentUnitsRemaining > 0;

    this.currentUsage = usage;
    return usage;
  }

  /**
   * Get usage from webapp
   */
  private async getUsageFromWebapp(): Promise<UserUsageStats | null> {
    try {
      const credentials = this.cloudService.getCredentials();
      if (!credentials?.accessToken) {
        return null;
      }

      const response = await fetch(`${this.cloudService.getApiUrl()}/api/extension/usage`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${credentials.accessToken}`,
        },
      });

      if (!response.ok) {
        return null;
      }

      const data = await response.json();
      return {
        cubentUnitsUsed: data.cubentUnitsUsed || 0,
        cubentUnitsLimit: data.cubentUnitsLimit || 50,
        cubentUnitsRemaining: Math.max(0, (data.cubentUnitsLimit || 50) - (data.cubentUnitsUsed || 0)),
        usagePercentage: Math.min(100, ((data.cubentUnitsUsed || 0) / (data.cubentUnitsLimit || 50)) * 100),
        canMakeRequest: (data.cubentUnitsUsed || 0) < (data.cubentUnitsLimit || 50),
        unitsResetDate: data.unitsResetDate ? new Date(data.unitsResetDate) : undefined,
      };
    } catch (error) {
      console.error('[TokenUsageService] Error getting usage from webapp:', error);
      return null;
    }
  }

  /**
   * Check if user can make a request with the given model
   */
  public async canMakeRequest(modelId: string): Promise<{ canMake: boolean; unitsRequired: number; unitsRemaining: number }> {
    const currentUsage = await this.getCurrentUsage();
    const unitsRequired = this.calculateCubentUnits(modelId);
    
    return {
      canMake: currentUsage.cubentUnitsRemaining >= unitsRequired,
      unitsRequired,
      unitsRemaining: currentUsage.cubentUnitsRemaining,
    };
  }

  /**
   * Check for usage warnings and limits
   */
  private async checkUsageWarnings(): Promise<void> {
    const usage = await this.getCurrentUsage();
    
    if (usage.usagePercentage >= 100) {
      this.emit('limit-exceeded', { usage });
    } else if (usage.usagePercentage >= 90) {
      this.emit('usage-warning', { usage, level: 'critical' });
    } else if (usage.usagePercentage >= 75) {
      this.emit('usage-warning', { usage, level: 'warning' });
    }
  }

  /**
   * Reset usage (for testing or manual reset)
   */
  public async resetUsage(): Promise<void> {
    await this.context.globalState.update('cubentUnitsUsage', {
      cubentUnitsUsed: 0,
      lastUpdated: new Date().toISOString(),
      unitsResetDate: new Date().toISOString(),
    });

    this.currentUsage = null;
    const usage = await this.getCurrentUsage();
    this.emit('usage-updated', { usage });
  }
}
