<!--
Thank you for contributing to Roo Code!

Before submitting your PR, please ensure:
- It's linked to an approved GitHub Issue.
- You've reviewed our [Contributing Guidelines](../CONTRIBUTING.md).
-->

### Related GitHub Issue

<!-- Every PR MUST be linked to an approved issue. -->

Closes: # <!-- Replace with the issue number, e.g., Closes: #123 -->

### Description

<!--
Briefly summarize the changes in this PR and how they address the linked issue.
The issue should cover the "what" and "why"; this section should focus on:
- The "how": key implementation details, design choices, or trade-offs made.
- Anything specific reviewers should pay attention to in this PR.
-->

### Test Procedure

<!--
Detail the steps to test your changes. This helps reviewers verify your work.
- How did you test this specific implementation? (e.g., unit tests, manual testing steps)
- How can reviewers reproduce your tests or verify the fix/feature?
- Include relevant testing environment details if applicable.
-->

### Type of Change

<!-- Mark all applicable boxes with an 'x'. -->

- [ ] 🐛 **Bug Fix**: Non-breaking change that fixes an issue.
- [ ] ✨ **New Feature**: Non-breaking change that adds functionality.
- [ ] 💥 **Breaking Change**: Fix or feature that would cause existing functionality to not work as expected.
- [ ] ♻️ **Refactor**: Code change that neither fixes a bug nor adds a feature.
- [ ] 💅 **Style**: Changes that do not affect the meaning of the code (white-space, formatting, etc.).
- [ ] 📚 **Documentation**: Updates to documentation files.
- [ ] ⚙️ **Build/CI**: Changes to the build process or CI configuration.
- [ ] 🧹 **Chore**: Other changes that don't modify `src` or test files.

### Pre-Submission Checklist

<!-- Go through this checklist before marking your PR as ready for review. -->

- [ ] **Issue Linked**: This PR is linked to an approved GitHub Issue (see "Related GitHub Issue" above).
- [ ] **Scope**: My changes are focused on the linked issue (one major feature/fix per PR).
- [ ] **Self-Review**: I have performed a thorough self-review of my code.
- [ ] **Code Quality**:
    - [ ] My code adheres to the project's style guidelines.
    - [ ] There are no new linting errors or warnings (`npm run lint`).
    - [ ] All debug code (e.g., `console.log`) has been removed.
- [ ] **Testing**:
    - [ ] New and/or updated tests have been added to cover my changes.
    - [ ] All tests pass locally (`npm test`).
    - [ ] The application builds successfully with my changes.
- [ ] **Branch Hygiene**: My branch is up-to-date (rebased) with the `main` branch.
- [ ] **Documentation Impact**: I have considered if my changes require documentation updates (see "Documentation Updates" section below).
- [ ] **Changeset**: A changeset has been created using `npm run changeset` if this PR includes user-facing changes or dependency updates.
- [ ] **Contribution Guidelines**: I have read and agree to the [Contributor Guidelines](/CONTRIBUTING.md).

### Screenshots / Videos

<!--
For UI changes, please provide before-and-after screenshots or a short video of the *actual results*.
This greatly helps in understanding the visual impact of your changes.
-->

### Documentation Updates

<!--
Does this PR necessitate updates to user-facing documentation?
- [ ] No documentation updates are required.
- [ ] Yes, documentation updates are required. (Please describe what needs to be updated or link to a PR in the docs repository).
-->

### Additional Notes

<!-- Add any other context, questions, or information for reviewers here. -->

### Get in Touch

<!--
Please provide your Discord username for reviewers or maintainers to reach you if they have questions about your PR
-->
